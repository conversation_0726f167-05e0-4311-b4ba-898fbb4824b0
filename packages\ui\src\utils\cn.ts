import { type ClassValue, clsx } from 'clsx';

/**
 * Utilitaire pour combiner les classes CSS avec clsx
 * Permet de gérer facilement les classes conditionnelles
 * 
 * @param inputs - Classes CSS à combiner
 * @returns String de classes CSS combinées
 * 
 * @example
 * cn('base-class', condition && 'conditional-class', { 'active': isActive })
 */
export function cn(...inputs: ClassValue[]) {
  return clsx(inputs);
}

/**
 * Utilitaire pour créer des variantes de composants avec class-variance-authority
 * Réexporté pour faciliter l'utilisation dans les composants
 */
export { cva, type VariantProps } from 'class-variance-authority';
