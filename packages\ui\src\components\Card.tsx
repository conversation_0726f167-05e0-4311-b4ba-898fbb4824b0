import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../utils/cn';

/**
 * Variantes du composant Card
 */
const cardVariants = cva(
  'bg-white rounded-lg border border-neutral-200 overflow-hidden',
  {
    variants: {
      shadow: {
        none: 'shadow-none',
        sm: 'shadow-sm',
        md: 'shadow-soft',
        lg: 'shadow-medium',
        xl: 'shadow-strong',
      },
      padding: {
        none: 'p-0',
        sm: 'p-4',
        md: 'p-6',
        lg: 'p-8',
      },
      hover: {
        true: 'hover:shadow-medium transition-shadow duration-200 cursor-pointer',
        false: '',
      },
    },
    defaultVariants: {
      shadow: 'md',
      padding: 'md',
      hover: false,
    },
  }
);

/**
 * Interface des props du composant Card
 */
export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  /** Contenu de la carte */
  children: React.ReactNode;
  /** Titre de la carte */
  title?: string;
  /** Description de la carte */
  description?: string;
  /** Actions à afficher dans l'en-tête */
  actions?: React.ReactNode;
  /** Contenu du pied de page */
  footer?: React.ReactNode;
}

/**
 * Composant Card du Design System SAMATRANSPORT
 * 
 * @example
 * <Card title="Réservation" description="Détails de votre voyage">
 *   <p>Contenu de la carte</p>
 * </Card>
 */
export const Card = React.forwardRef<HTMLDivElement, CardProps>(
  (
    {
      className,
      shadow,
      padding,
      hover,
      children,
      title,
      description,
      actions,
      footer,
      ...props
    },
    ref
  ) => {
    const hasHeader = title || description || actions;
    const cardPadding = hasHeader || footer ? 'none' : padding;

    return (
      <div
        className={cn(cardVariants({ shadow, padding: cardPadding, hover, className }))}
        ref={ref}
        {...props}
      >
        {/* En-tête de la carte */}
        {hasHeader && (
          <div className="px-6 py-4 border-b border-neutral-200">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                {title && (
                  <h3 className="text-lg font-semibold text-neutral-900 truncate">
                    {title}
                  </h3>
                )}
                {description && (
                  <p className="mt-1 text-sm text-neutral-600">
                    {description}
                  </p>
                )}
              </div>
              {actions && (
                <div className="ml-4 flex-shrink-0">
                  {actions}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Contenu principal */}
        <div className={cn(
          hasHeader || footer ? 'px-6 py-4' : '',
          padding === 'sm' && 'p-4',
          padding === 'md' && 'p-6',
          padding === 'lg' && 'p-8'
        )}>
          {children}
        </div>

        {/* Pied de page */}
        {footer && (
          <div className="px-6 py-4 bg-neutral-50 border-t border-neutral-200">
            {footer}
          </div>
        )}
      </div>
    );
  }
);

Card.displayName = 'Card';

/**
 * Composant CardHeader pour une structure plus flexible
 */
export const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('px-6 py-4 border-b border-neutral-200', className)}
    {...props}
  />
));

CardHeader.displayName = 'CardHeader';

/**
 * Composant CardContent pour le contenu principal
 */
export const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('px-6 py-4', className)}
    {...props}
  />
));

CardContent.displayName = 'CardContent';

/**
 * Composant CardFooter pour le pied de page
 */
export const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('px-6 py-4 bg-neutral-50 border-t border-neutral-200', className)}
    {...props}
  />
));

CardFooter.displayName = 'CardFooter';

export default Card;
