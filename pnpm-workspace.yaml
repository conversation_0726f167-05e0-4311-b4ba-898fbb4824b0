packages:
  - 'apps/*'
  - 'packages/*'
  - 'supabase'

# Configuration pnpm pour le monorepo SAMATRANSPORT
catalog:
  # React ecosystem
  react: ^18.2.0
  react-dom: ^18.2.0
  next: ^14.0.4
  
  # TypeScript
  typescript: ^5.3.3
  '@types/react': ^18.2.45
  '@types/react-dom': ^18.2.18
  '@types/node': ^20.10.5
  
  # Styling
  tailwindcss: ^3.3.6
  '@tailwindcss/forms': ^0.5.7
  '@tailwindcss/typography': ^0.5.10
  
  # UI Components
  '@headlessui/react': ^1.7.17
  '@heroicons/react': ^2.0.18
  lucide-react: ^0.294.0
  
  # State Management & Data Fetching
  '@tanstack/react-query': ^5.14.2
  zustand: ^4.4.7
  
  # Forms & Validation
  'react-hook-form': ^7.48.2
  zod: ^3.22.4
  '@hookform/resolvers': ^3.3.2
  
  # Authentication
  '@supabase/supabase-js': ^2.38.5
  '@supabase/auth-helpers-nextjs': ^0.8.7
  next-auth: ^4.24.5
  
  # Internationalization
  next-intl: ^3.3.2
  
  # Date & Time
  date-fns: ^2.30.0
  
  # Utilities
  clsx: ^2.0.0
  class-variance-authority: ^0.7.0
  
  # Development
  eslint: ^8.55.0
  prettier: ^3.1.1
  '@typescript-eslint/eslint-plugin': ^6.14.0
  '@typescript-eslint/parser': ^6.14.0
  
  # Testing
  '@testing-library/react': ^14.1.2
  '@testing-library/jest-dom': ^6.1.5
  vitest: ^1.0.4
  
  # Storybook
  '@storybook/react': ^7.6.4
  '@storybook/nextjs': ^7.6.4
