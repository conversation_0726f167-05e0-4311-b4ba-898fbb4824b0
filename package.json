{"name": "samatransport-ecosystem", "version": "1.0.0", "description": "Écosystème intégré d'applications pour le transport routier en Afrique de l'Ouest", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "test": "turbo run test", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "clean": "turbo run clean", "type-check": "turbo run type-check", "prepare": "husky install"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "eslint": "^8.55.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.1.1", "turbo": "^1.11.2", "typescript": "^5.3.3"}, "packageManager": "pnpm@8.12.1", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/digitalbridge/samatransport-ecosystem.git"}, "author": "DigitalBridge", "license": "MIT", "keywords": ["transport", "logistics", "africa", "monorepo", "nextjs", "supabase"]}