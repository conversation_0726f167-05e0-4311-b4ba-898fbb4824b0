import type { Config } from "tailwindcss";

export default {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    // Inclure les composants du Design System
    "../../packages/ui/src/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  // Étendre la configuration du Design System
  presets: [require("../../packages/ui/tailwind.config.js")],
  theme: {
    extend: {
      // Extensions spécifiques à l'app Control si nécessaire
    },
  },
  plugins: [],
} satisfies Config;
