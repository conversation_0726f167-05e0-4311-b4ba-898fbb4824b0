import {
  ChartBarIcon,
  TruckIcon,
  UsersIcon,
  CurrencyDollarIcon,
  ClockIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

export default function ControlDashboard() {
  return (
    <div className="admin-layout">
      {/* Header */}
      <header className="admin-header">
        <div className="container-samatransport">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <TruckIcon className="h-8 w-8 text-primary-600" />
                <h1 className="text-xl font-bold text-neutral-900">
                  SAMATRANSPORT Control
                </h1>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="text-sm text-neutral-600">
                Bienvenue, Administrateur
              </div>
              <div className="h-8 w-8 bg-primary-100 rounded-full flex items-center justify-center">
                <span className="text-primary-600 font-medium text-sm">A</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="admin-content">
        <div className="container-samatransport">
          {/* Page Title */}
          <div className="mb-8">
            <h2 className="admin-section-title">
              Tableau de Bord - Centre de Commande
            </h2>
            <p className="text-neutral-600">
              Vue d'ensemble des opérations et de la performance de votre flotte
            </p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="admin-card">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TruckIcon className="h-8 w-8 text-primary-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-neutral-600">
                    Véhicules Actifs
                  </p>
                  <p className="text-2xl font-semibold text-neutral-900">24</p>
                </div>
              </div>
            </div>

            <div className="admin-card">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <UsersIcon className="h-8 w-8 text-secondary-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-neutral-600">
                    Passagers Aujourd'hui
                  </p>
                  <p className="text-2xl font-semibold text-neutral-900">1,247</p>
                </div>
              </div>
            </div>

            <div className="admin-card">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CurrencyDollarIcon className="h-8 w-8 text-accent-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-neutral-600">
                    Revenus du Jour
                  </p>
                  <p className="text-2xl font-semibold text-neutral-900">
                    2,450,000 CFA
                  </p>
                </div>
              </div>
            </div>

            <div className="admin-card">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <ChartBarIcon className="h-8 w-8 text-success-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-neutral-600">
                    Taux de Remplissage
                  </p>
                  <p className="text-2xl font-semibold text-neutral-900">87%</p>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div className="admin-card">
              <h3 className="admin-section-subtitle">Actions Rapides</h3>
              <div className="grid grid-cols-2 gap-4">
                <button className="p-4 border border-neutral-200 rounded-lg hover:bg-neutral-50 transition-colors">
                  <TruckIcon className="h-6 w-6 text-primary-600 mb-2" />
                  <span className="text-sm font-medium">Ajouter Véhicule</span>
                </button>
                <button className="p-4 border border-neutral-200 rounded-lg hover:bg-neutral-50 transition-colors">
                  <UsersIcon className="h-6 w-6 text-secondary-600 mb-2" />
                  <span className="text-sm font-medium">Gérer Agents</span>
                </button>
                <button className="p-4 border border-neutral-200 rounded-lg hover:bg-neutral-50 transition-colors">
                  <ClockIcon className="h-6 w-6 text-accent-600 mb-2" />
                  <span className="text-sm font-medium">Planifier Voyage</span>
                </button>
                <button className="p-4 border border-neutral-200 rounded-lg hover:bg-neutral-50 transition-colors">
                  <ChartBarIcon className="h-6 w-6 text-success-600 mb-2" />
                  <span className="text-sm font-medium">Voir Rapports</span>
                </button>
              </div>
            </div>

            <div className="admin-card">
              <h3 className="admin-section-subtitle">Alertes & Notifications</h3>
              <div className="space-y-3">
                <div className="flex items-start space-x-3 p-3 bg-warning-50 border border-warning-200 rounded-lg">
                  <ExclamationTriangleIcon className="h-5 w-5 text-warning-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-warning-800">
                      Maintenance Requise
                    </p>
                    <p className="text-xs text-warning-600">
                      Véhicule CI-2024-AB nécessite une révision
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3 p-3 bg-error-50 border border-error-200 rounded-lg">
                  <ExclamationTriangleIcon className="h-5 w-5 text-error-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-error-800">
                      Retard Signalé
                    </p>
                    <p className="text-xs text-error-600">
                      Voyage Abidjan-Bouaké de 14h30 en retard de 45min
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3 p-3 bg-success-50 border border-success-200 rounded-lg">
                  <ChartBarIcon className="h-5 w-5 text-success-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-success-800">
                      Objectif Atteint
                    </p>
                    <p className="text-xs text-success-600">
                      Revenus mensuels dépassent l'objectif de 15%
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
