/**
 * Utilitaires de formatage pour l'écosystème SAMATRANSPORT
 */

/**
 * Formate un montant en devise avec le symbole approprié
 */
export function formatCurrency(
  amount: number,
  currency: string = 'XOF',
  locale: string = 'fr-CI'
): string {
  const currencyMap: Record<string, string> = {
    XOF: 'XOF', // Franc CFA Ouest Africain
    GNF: 'GNF', // Franc Guinéen
    LRD: 'LRD', // Dollar Libérien
    SLL: 'SLL', // Leone Sierra-Léonais
    EUR: 'EUR', // Euro
    USD: 'USD', // Dollar Américain
  };

  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currencyMap[currency] || currency,
      minimumFractionDigits: currency === 'XOF' || currency === 'GNF' ? 0 : 2,
    }).format(amount);
  } catch (error) {
    // Fallback si la devise n'est pas supportée
    return `${amount.toLocaleString(locale)} ${currency}`;
  }
}

/**
 * Formate une date selon les conventions locales
 */
export function formatDate(
  date: Date | string,
  options: Intl.DateTimeFormatOptions = {},
  locale: string = 'fr-CI'
): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    ...options,
  };

  return new Intl.DateTimeFormat(locale, defaultOptions).format(dateObj);
}

/**
 * Formate une heure
 */
export function formatTime(
  date: Date | string,
  locale: string = 'fr-CI'
): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  return new Intl.DateTimeFormat(locale, {
    hour: '2-digit',
    minute: '2-digit',
  }).format(dateObj);
}

/**
 * Formate une durée en heures et minutes
 */
export function formatDuration(minutes: number): string {
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (hours === 0) {
    return `${remainingMinutes}min`;
  }
  
  if (remainingMinutes === 0) {
    return `${hours}h`;
  }
  
  return `${hours}h ${remainingMinutes}min`;
}

/**
 * Formate un numéro de téléphone
 */
export function formatPhoneNumber(phone: string, countryCode: string = '+225'): string {
  // Supprime tous les caractères non numériques
  const cleaned = phone.replace(/\D/g, '');
  
  // Formate selon le pays (exemple pour la Côte d'Ivoire)
  if (countryCode === '+225' && cleaned.length === 10) {
    return `${countryCode} ${cleaned.slice(0, 2)} ${cleaned.slice(2, 4)} ${cleaned.slice(4, 6)} ${cleaned.slice(6, 8)} ${cleaned.slice(8)}`;
  }
  
  // Format générique
  return `${countryCode} ${cleaned}`;
}

/**
 * Formate un numéro de siège
 */
export function formatSeatNumber(seatNumber: string | number): string {
  return `Siège ${seatNumber}`;
}

/**
 * Formate un code de réservation
 */
export function formatBookingCode(code: string): string {
  return code.toUpperCase().replace(/(.{3})/g, '$1-').slice(0, -1);
}

/**
 * Formate une distance
 */
export function formatDistance(kilometers: number): string {
  if (kilometers < 1) {
    return `${Math.round(kilometers * 1000)}m`;
  }
  
  return `${kilometers.toLocaleString('fr-CI')} km`;
}

/**
 * Formate un poids
 */
export function formatWeight(kilograms: number): string {
  if (kilograms < 1) {
    return `${Math.round(kilograms * 1000)}g`;
  }
  
  return `${kilograms.toLocaleString('fr-CI')} kg`;
}

/**
 * Formate un pourcentage
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  return `${value.toFixed(decimals)}%`;
}

/**
 * Tronque un texte avec des points de suspension
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) {
    return text;
  }
  
  return `${text.slice(0, maxLength - 3)}...`;
}

/**
 * Capitalise la première lettre d'une chaîne
 */
export function capitalize(text: string): string {
  return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
}

/**
 * Formate un nom complet
 */
export function formatFullName(firstName: string, lastName: string): string {
  return `${capitalize(firstName)} ${lastName.toUpperCase()}`;
}
