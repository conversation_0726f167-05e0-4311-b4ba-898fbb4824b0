import { z } from 'zod';

/**
 * Schémas de validation Zod pour l'écosystème SAMATRANSPORT
 */

// Validation des devises supportées
export const currencySchema = z.enum(['XOF', 'GNF', 'LRD', 'SLL', 'EUR', 'USD']);

// Validation des langues supportées
export const languageSchema = z.enum(['fr', 'en']);

// Validation d'un numéro de téléphone
export const phoneSchema = z.string()
  .min(8, 'Le numéro de téléphone doit contenir au moins 8 chiffres')
  .max(15, 'Le numéro de téléphone ne peut pas dépasser 15 chiffres')
  .regex(/^[\d\s\-\+\(\)]+$/, 'Format de numéro de téléphone invalide');

// Validation d'un email
export const emailSchema = z.string()
  .email('Adresse email invalide')
  .min(1, 'L\'adresse email est requise');

// Validation d'un mot de passe
export const passwordSchema = z.string()
  .min(8, 'Le mot de passe doit contenir au moins 8 caractères')
  .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre');

// Validation d'un montant
export const amountSchema = z.number()
  .positive('Le montant doit être positif')
  .max(10000000, 'Le montant ne peut pas dépasser 10 000 000');

// Validation d'une date
export const dateSchema = z.date()
  .refine((date) => date > new Date(), 'La date doit être dans le futur');

// Validation d'un code de réservation
export const bookingCodeSchema = z.string()
  .length(6, 'Le code de réservation doit contenir exactement 6 caractères')
  .regex(/^[A-Z0-9]+$/, 'Le code de réservation ne peut contenir que des lettres majuscules et des chiffres');

// Validation d'un numéro de siège
export const seatNumberSchema = z.string()
  .regex(/^[A-Z]?\d{1,2}[A-Z]?$/, 'Format de numéro de siège invalide');

// Validation d'un nom
export const nameSchema = z.string()
  .min(2, 'Le nom doit contenir au moins 2 caractères')
  .max(50, 'Le nom ne peut pas dépasser 50 caractères')
  .regex(/^[a-zA-ZÀ-ÿ\s\-']+$/, 'Le nom ne peut contenir que des lettres, espaces, tirets et apostrophes');

// Validation d'une plaque d'immatriculation
export const licensePlateSchema = z.string()
  .min(4, 'La plaque d\'immatriculation doit contenir au moins 4 caractères')
  .max(10, 'La plaque d\'immatriculation ne peut pas dépasser 10 caractères')
  .regex(/^[A-Z0-9\s\-]+$/, 'Format de plaque d\'immatriculation invalide');

// Validation d'un kilométrage
export const mileageSchema = z.number()
  .min(0, 'Le kilométrage ne peut pas être négatif')
  .max(2000000, 'Le kilométrage ne peut pas dépasser 2 000 000 km');

// Validation d'une capacité de véhicule
export const vehicleCapacitySchema = z.number()
  .int('La capacité doit être un nombre entier')
  .min(1, 'La capacité doit être d\'au moins 1 place')
  .max(100, 'La capacité ne peut pas dépasser 100 places');

// Validation d'un poids de colis
export const packageWeightSchema = z.number()
  .positive('Le poids doit être positif')
  .max(1000, 'Le poids ne peut pas dépasser 1000 kg');

// Validation d'une adresse
export const addressSchema = z.object({
  street: z.string().min(5, 'L\'adresse doit contenir au moins 5 caractères'),
  city: z.string().min(2, 'La ville doit contenir au moins 2 caractères'),
  country: z.string().min(2, 'Le pays doit contenir au moins 2 caractères'),
  postalCode: z.string().optional(),
});

// Validation d'un utilisateur
export const userSchema = z.object({
  firstName: nameSchema,
  lastName: nameSchema,
  email: emailSchema,
  phone: phoneSchema,
  password: passwordSchema.optional(),
});

// Validation d'un véhicule
export const vehicleSchema = z.object({
  licensePlate: licensePlateSchema,
  model: z.string().min(2, 'Le modèle doit contenir au moins 2 caractères'),
  capacity: vehicleCapacitySchema,
  mileage: mileageSchema,
  type: z.enum(['STANDARD', 'VIP']),
});

// Validation d'un itinéraire
export const routeSchema = z.object({
  origin: z.string().min(2, 'L\'origine doit contenir au moins 2 caractères'),
  destination: z.string().min(2, 'La destination doit contenir au moins 2 caractères'),
  distance: z.number().positive('La distance doit être positive'),
  estimatedDuration: z.number().positive('La durée estimée doit être positive'),
});

// Validation d'un tarif
export const priceSchema = z.object({
  amount: amountSchema,
  currency: currencySchema,
  type: z.enum(['ADULT', 'CHILD', 'STUDENT', 'SENIOR']),
});

// Validation d'un colis
export const packageSchema = z.object({
  senderName: nameSchema,
  senderPhone: phoneSchema,
  recipientName: nameSchema,
  recipientPhone: phoneSchema,
  weight: packageWeightSchema,
  description: z.string().min(5, 'La description doit contenir au moins 5 caractères'),
  declaredValue: amountSchema.optional(),
});

/**
 * Fonctions utilitaires de validation
 */

// Valide si une chaîne est un email valide
export function isValidEmail(email: string): boolean {
  return emailSchema.safeParse(email).success;
}

// Valide si une chaîne est un numéro de téléphone valide
export function isValidPhone(phone: string): boolean {
  return phoneSchema.safeParse(phone).success;
}

// Valide si un montant est valide
export function isValidAmount(amount: number): boolean {
  return amountSchema.safeParse(amount).success;
}

// Valide si une date est dans le futur
export function isValidFutureDate(date: Date): boolean {
  return dateSchema.safeParse(date).success;
}
