/**
 * Types et interfaces communes pour l'écosystème SAMATRANSPORT
 */

// Types de base
export type Currency = 'XOF' | 'GNF' | 'LRD' | 'SLL' | 'EUR' | 'USD';
export type Language = 'fr' | 'en';

// Types utilisateur
export type UserRole = 
  | 'SUPER_ADMIN'
  | 'ADMIN'
  | 'MANAGER'
  | 'AGENT'
  | 'DRIVER'
  | 'CONTROLLER'
  | 'CLIENT';

// Types véhicule
export type VehicleType = 'STANDARD' | 'VIP';
export type VehicleStatus = 'ACTIVE' | 'MAINTENANCE' | 'OUT_OF_SERVICE';

// Types réservation
export type BookingStatus = 
  | 'PENDING'
  | 'CONFIRMED'
  | 'PAID'
  | 'CHECKED_IN'
  | 'COMPLETED'
  | 'CANCELLED'
  | 'REFUNDED';

export type SeatStatus = 'AVAILABLE' | 'RESERVED' | 'OCCUPIED' | 'BLOCKED';

// Types colis
export type PackageStatus = 
  | 'REGISTERED'
  | 'IN_TRANSIT'
  | 'ARRIVED'
  | 'DELIVERED'
  | 'RETURNED'
  | 'LOST';

// Types paiement
export type PaymentMethod = 
  | 'CASH'
  | 'ORANGE_MONEY'
  | 'MTN_MONEY'
  | 'MOOV_MONEY'
  | 'WAVE'
  | 'BANK_CARD'
  | 'BANK_TRANSFER';

export type PaymentStatus = 
  | 'PENDING'
  | 'PROCESSING'
  | 'COMPLETED'
  | 'FAILED'
  | 'CANCELLED'
  | 'REFUNDED';

// Types de voyage
export type TripStatus = 
  | 'SCHEDULED'
  | 'BOARDING'
  | 'DEPARTED'
  | 'IN_TRANSIT'
  | 'ARRIVED'
  | 'COMPLETED'
  | 'CANCELLED'
  | 'DELAYED';

// Types de maintenance
export type MaintenanceType = 
  | 'PREVENTIVE'
  | 'CORRECTIVE'
  | 'EMERGENCY'
  | 'INSPECTION';

export type MaintenanceStatus = 
  | 'SCHEDULED'
  | 'IN_PROGRESS'
  | 'COMPLETED'
  | 'CANCELLED';

// Interfaces de base
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface User extends BaseEntity {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  role: UserRole;
  isActive: boolean;
  lastLoginAt?: Date;
}

export interface Agency extends BaseEntity {
  name: string;
  code: string;
  address: string;
  city: string;
  country: string;
  phone: string;
  email?: string;
  currency: Currency;
  isActive: boolean;
}

export interface Vehicle extends BaseEntity {
  licensePlate: string;
  model: string;
  brand: string;
  year: number;
  capacity: number;
  type: VehicleType;
  status: VehicleStatus;
  mileage: number;
  agencyId: string;
}

export interface Route extends BaseEntity {
  name: string;
  origin: string;
  destination: string;
  distance: number;
  estimatedDuration: number; // en minutes
  isActive: boolean;
}

export interface Schedule extends BaseEntity {
  routeId: string;
  departureTime: string; // Format HH:MM
  arrivalTime: string; // Format HH:MM
  daysOfWeek: number[]; // 0-6 (Dimanche-Samedi)
  isActive: boolean;
}

export interface Trip extends BaseEntity {
  scheduleId: string;
  vehicleId: string;
  driverId: string;
  date: Date;
  departureTime: Date;
  arrivalTime?: Date;
  status: TripStatus;
  availableSeats: number;
  price: number;
  currency: Currency;
}

export interface Booking extends BaseEntity {
  tripId: string;
  userId: string;
  passengerName: string;
  passengerPhone: string;
  seatNumber: string;
  status: BookingStatus;
  totalAmount: number;
  currency: Currency;
  bookingCode: string;
  paymentMethod?: PaymentMethod;
  paymentStatus?: PaymentStatus;
}

export interface Package extends BaseEntity {
  trackingNumber: string;
  senderName: string;
  senderPhone: string;
  recipientName: string;
  recipientPhone: string;
  originAgencyId: string;
  destinationAgencyId: string;
  weight: number;
  description: string;
  declaredValue?: number;
  status: PackageStatus;
  shippingCost: number;
  currency: Currency;
}

export interface Payment extends BaseEntity {
  bookingId?: string;
  packageId?: string;
  amount: number;
  currency: Currency;
  method: PaymentMethod;
  status: PaymentStatus;
  transactionId?: string;
  reference?: string;
  processedAt?: Date;
}

export interface Maintenance extends BaseEntity {
  vehicleId: string;
  type: MaintenanceType;
  status: MaintenanceStatus;
  description: string;
  scheduledDate: Date;
  completedDate?: Date;
  cost?: number;
  currency?: Currency;
  mileageAtMaintenance: number;
  nextMaintenanceKm?: number;
  nextMaintenanceDate?: Date;
}

// Types pour les formulaires
export interface CreateBookingData {
  tripId: string;
  passengerName: string;
  passengerPhone: string;
  seatNumber: string;
  paymentMethod: PaymentMethod;
}

export interface CreatePackageData {
  senderName: string;
  senderPhone: string;
  recipientName: string;
  recipientPhone: string;
  originAgencyId: string;
  destinationAgencyId: string;
  weight: number;
  description: string;
  declaredValue?: number;
}

export interface CreateVehicleData {
  licensePlate: string;
  model: string;
  brand: string;
  year: number;
  capacity: number;
  type: VehicleType;
  mileage: number;
  agencyId: string;
}

// Types pour les réponses API
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Types pour les filtres et recherches
export interface BookingFilters {
  status?: BookingStatus;
  dateFrom?: Date;
  dateTo?: Date;
  agencyId?: string;
  search?: string;
}

export interface PackageFilters {
  status?: PackageStatus;
  originAgencyId?: string;
  destinationAgencyId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  search?: string;
}

export interface TripFilters {
  routeId?: string;
  date?: Date;
  status?: TripStatus;
  agencyId?: string;
}
