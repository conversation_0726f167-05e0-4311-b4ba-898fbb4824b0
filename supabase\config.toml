# Configuration Supabase pour l'écosystème SAMATRANSPORT

[api]
enabled = true
port = 54321
schemas = ["public", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[db]
port = 54322
shadow_port = 54320
major_version = 15

[studio]
enabled = true
port = 54323
api_url = "http://localhost:54321"

[inbucket]
enabled = true
port = 54324
smtp_port = 54325
pop3_port = 54326

[storage]
enabled = true
port = 54327
file_size_limit = "50MiB"
image_transformation = { enabled = true }

[auth]
enabled = true
site_url = "http://localhost:3000"
additional_redirect_urls = ["https://localhost:3000"]
jwt_expiry = 3600
enable_signup = true
enable_email_confirmations = false
enable_sms_confirmations = false

[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = false

[auth.sms]
enable_signup = false
enable_confirmations = false

# Configuration des providers OAuth (optionnel pour plus tard)
[auth.external.google]
enabled = false
client_id = ""
secret = ""
redirect_uri = "http://localhost:54321/auth/v1/callback"

[edge_functions]
enabled = true
port = 54328

[analytics]
enabled = false

[functions.shared]
verify_jwt = false
