# 🚌 SAMATRANSPORT Ecosystem

> Écosystème intégré d'applications pour moderniser le transport routier en Afrique de l'Ouest

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![Next.js](https://img.shields.io/badge/Next.js-000000?logo=next.js&logoColor=white)](https://nextjs.org/)
[![Supabase](https://img.shields.io/badge/Supabase-3ECF8E?logo=supabase&logoColor=white)](https://supabase.com/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-38B2AC?logo=tailwind-css&logoColor=white)](https://tailwindcss.com/)

## 📋 Vue d'ensemble

SAMATRANSPORT est une suite d'applications modulaires conçue pour optimiser la gestion des compagnies de transport routier de passagers et de marchandises en Afrique de l'Ouest. L'écosystème offre une solution digitale complète pour améliorer l'efficacité opérationnelle, sécuriser les revenus et offrir une expérience client de qualité supérieure.

### 🎯 Objectifs Principaux

- **Optimisation de la gestion** des opérations de transport
- **Amélioration de l'efficacité opérationnelle**
- **Sécurisation des revenus**
- **Renforcement de la maintenance** de la flotte
- **Expérience client de qualité supérieure**

## 🏗️ Architecture

### Applications de l'Écosystème

| Application | Description | Port | Statut |
|-------------|-------------|------|--------|
| **Control** | Centre de commande et d'administration | 3001 | ✅ En développement |
| **Guichet** | Point de vente physique et opérations | 3002 | 🔄 Planifié |
| **Site Web** | Portail public et espace client | 3000 | 🔄 Planifié |
| **Courrier** | Gestion de l'expédition de colis | 3003 | 🔄 Planifié |
| **Finance** | Pilotage économique et analyse | 3004 | 🔄 Planifié |
| **Mobile Agent** | Application mobile Android | - | 🔄 Futur |

### Stack Technologique

- **Frontend**: Next.js 14, React 18, TypeScript
- **Backend**: Supabase (PostgreSQL + Edge Functions)
- **Styling**: Tailwind CSS, Design System personnalisé
- **State Management**: Zustand, React Query
- **Authentication**: NextAuth.js / Supabase Auth
- **Monorepo**: pnpm workspaces, Turbo
- **Testing**: Vitest, Testing Library
- **CI/CD**: GitHub Actions

## 🚀 Démarrage Rapide

### Prérequis

- Node.js 18+ 
- pnpm 8+
- Git

### Installation

```bash
# Cloner le repository
git clone https://github.com/digitalbridge/samatransport-ecosystem.git
cd samatransport-ecosystem

# Installer les dépendances
pnpm install

# Copier les variables d'environnement
cp .env.example .env.local

# Démarrer l'application Control en mode développement
pnpm dev
```

### Configuration Supabase

1. Créer un projet Supabase
2. Configurer les variables d'environnement dans `.env.local`
3. Exécuter les migrations :

```bash
# Installer Supabase CLI
npm install -g supabase

# Initialiser Supabase
supabase init

# Démarrer Supabase localement
supabase start

# Appliquer les migrations
supabase db push
```

## 📁 Structure du Projet

```
SAMATRANSPORT_ECOSYSTEM/
├── apps/                     # Applications Next.js
│   ├── control-app/         # Application Control
│   ├── guichet-app/         # Application Guichet (à venir)
│   ├── site-web-client-app/ # Site Web & Espace Client (à venir)
│   ├── courrier-app/        # Application Courrier (à venir)
│   └── finance-app/         # Application Finance (à venir)
├── packages/                # Packages partagés
│   ├── ui/                  # Design System SAMATRANSPORT
│   ├── lib-core/           # Logique métier partagée
│   ├── eslint-config-custom/
│   ├── prettier-config-custom/
│   └── tsconfig/
├── supabase/               # Configuration Backend
│   ├── functions/          # Edge Functions
│   ├── migrations/         # Migrations DB
│   └── seed/              # Données de test
├── docs/                   # Documentation
└── .github/               # CI/CD GitHub Actions
```

## 🎨 Design System

Le Design System SAMATRANSPORT (`@samatransport/ui`) fournit :

- **Composants React** réutilisables
- **Palette de couleurs** adaptée au contexte africain
- **Typographie** optimisée (Inter + JetBrains Mono)
- **Tokens de design** cohérents
- **Documentation Storybook**

### Utilisation

```tsx
import { Button, Card, Input } from '@samatransport/ui';

function MyComponent() {
  return (
    <Card title="Réservation">
      <Input label="Nom complet" required />
      <Button variant="primary">Réserver</Button>
    </Card>
  );
}
```

## 🌍 Fonctionnalités Transversales

### Support Multi-devises
- XOF (Franc CFA Ouest Africain)
- GNF (Franc Guinéen)
- LRD (Dollar Libérien)
- SLL (Leone Sierra-Léonais)
- EUR, USD

### Support Multilingue
- Français (par défaut)
- Anglais

### Paiements Intégrés
- Orange Money
- MTN Mobile Money
- Moov Money
- Wave
- Cartes bancaires

## 📱 Applications

### Application Control
**Centre de commande et d'administration**

- Gestion des données maîtres
- Configuration des règles métier
- Planification et assignation des véhicules
- Gestion de la maintenance (FMS)
- Supervision et sécurité
- Journal d'audit immuable

### Application Guichet (À venir)
**Point de vente physique**

- Vente de billets optimisée
- Gestion des manifestes
- Mode hors-ligne robuste
- Synchronisation temps réel

### Site Web & Espace Client (À venir)
**Portail public et client**

- Réservation en ligne
- Sélection de sièges temps réel
- Billets électroniques
- Programme de fidélité

## 🛠️ Développement

### Scripts Disponibles

```bash
# Développement
pnpm dev              # Démarrer toutes les apps
pnpm dev:control      # Démarrer uniquement Control

# Build
pnpm build            # Build toutes les apps
pnpm build:control    # Build uniquement Control

# Tests
pnpm test             # Lancer tous les tests
pnpm test:watch       # Tests en mode watch

# Linting & Formatting
pnpm lint             # Linter tout le code
pnpm format           # Formatter tout le code

# Nettoyage
pnpm clean            # Nettoyer tous les builds
```

### Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/amazing-feature`)
3. Commit les changements (`git commit -m 'Add amazing feature'`)
4. Push vers la branche (`git push origin feature/amazing-feature`)
5. Ouvrir une Pull Request

## 📚 Documentation

- [Product Requirements Document (PRD)](./PRD.md)
- [Cahier des charges](./Cahier%20des%20charges.txt)
- [Architecture détaillée](./docs/02_ARCHITECTURE_CONVENTIONS/architecture.md)
- [Guide de développement](./docs/04_AI_AGENT_GUIDES/)

## 🤝 Support

- **Email**: <EMAIL>
- **Documentation**: [docs/](./docs/)
- **Issues**: [GitHub Issues](https://github.com/digitalbridge/samatransport-ecosystem/issues)

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier [LICENSE](./LICENSE) pour plus de détails.

---

**Développé avec ❤️ par [DigitalBridge](https://digitalbridge.ci) pour l'Afrique de l'Ouest**
