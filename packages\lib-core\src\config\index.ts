/**
 * Configuration globale de l'écosystème SAMATRANSPORT
 */

export const config = {
  // Configuration Supabase
  supabase: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  },

  // Configuration NextAuth
  auth: {
    secret: process.env.NEXTAUTH_SECRET || '',
    url: process.env.NEXTAUTH_URL || 'http://localhost:3000',
  },

  // Configuration des devises
  currencies: {
    default: 'XOF' as const,
    supported: ['XOF', 'GNF', 'LRD', 'SLL', 'EUR', 'USD'] as const,
    symbols: {
      XOF: 'CFA',
      GNF: 'GNF',
      LRD: 'L$',
      SLL: 'Le',
      EUR: '€',
      USD: '$',
    },
  },

  // Configuration des langues
  languages: {
    default: 'fr' as const,
    supported: ['fr', 'en'] as const,
  },

  // Configuration des paiements
  payments: {
    methods: [
      'CASH',
      'ORANGE_MONEY',
      'MTN_MONEY',
      'MOOV_MONEY',
      'WAVE',
      'BANK_CARD',
      'BANK_TRANSFER',
    ] as const,
    gateways: {
      orangeMoney: {
        apiKey: process.env.ORANGE_MONEY_API_KEY || '',
        secret: process.env.ORANGE_MONEY_SECRET || '',
        baseUrl: process.env.ORANGE_MONEY_BASE_URL || '',
      },
      mtnMoney: {
        apiKey: process.env.MTN_API_KEY || '',
        secret: process.env.MTN_SECRET || '',
        baseUrl: process.env.MTN_BASE_URL || '',
      },
      wave: {
        apiKey: process.env.WAVE_API_KEY || '',
        secret: process.env.WAVE_SECRET || '',
        baseUrl: process.env.WAVE_BASE_URL || '',
      },
    },
  },

  // Configuration des notifications
  notifications: {
    sms: {
      provider: process.env.SMS_PROVIDER || 'twilio',
      apiKey: process.env.SMS_PROVIDER_API_KEY || '',
      secret: process.env.SMS_PROVIDER_SECRET || '',
    },
    email: {
      smtp: {
        host: process.env.SMTP_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.SMTP_PORT || '587'),
        user: process.env.SMTP_USER || '',
        password: process.env.SMTP_PASSWORD || '',
      },
    },
  },

  // Configuration Redis (pour la synchronisation temps réel)
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
  },

  // Configuration de l'API de taux de change
  exchangeRate: {
    apiKey: process.env.EXCHANGE_RATE_API_KEY || '',
    baseUrl: 'https://api.exchangerate-api.com/v4/latest/',
    updateInterval: 3600000, // 1 heure en millisecondes
  },

  // Configuration du stockage de fichiers
  storage: {
    bucket: process.env.NEXT_PUBLIC_STORAGE_BUCKET || 'samatransport-files',
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'application/pdf'],
  },

  // Configuration de l'application
  app: {
    name: 'SAMATRANSPORT',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    baseUrl: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    supportEmail: '<EMAIL>',
    supportPhone: '+225 XX XX XX XX XX',
  },

  // Configuration des limites
  limits: {
    maxSeatsPerBooking: 10,
    maxPackageWeight: 1000, // kg
    maxBookingDaysInAdvance: 90,
    sessionTimeout: 30 * 60 * 1000, // 30 minutes
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15 minutes
  },

  // Configuration des codes de réservation
  booking: {
    codeLength: 6,
    codePrefix: 'ST',
    expirationHours: 24,
  },

  // Configuration des QR codes
  qrCode: {
    size: 200,
    errorCorrectionLevel: 'M' as const,
    margin: 4,
  },

  // Configuration de la maintenance
  maintenance: {
    preventiveIntervalKm: 10000,
    preventiveIntervalDays: 90,
    alertThresholdKm: 1000,
    alertThresholdDays: 7,
  },

  // Configuration des rapports
  reports: {
    defaultDateRange: 30, // jours
    maxExportRows: 10000,
    cacheTimeout: 5 * 60 * 1000, // 5 minutes
  },
} as const;

export type Config = typeof config;

// Utilitaires pour accéder à la configuration
export const getConfig = <T extends keyof Config>(key: T): Config[T] => {
  return config[key];
};

export const isProduction = () => config.app.environment === 'production';
export const isDevelopment = () => config.app.environment === 'development';
export const isTest = () => config.app.environment === 'test';

export default config;
