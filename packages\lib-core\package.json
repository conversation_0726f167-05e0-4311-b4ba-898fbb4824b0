{"name": "@samatransport/lib-core", "version": "0.1.0", "description": "Bibliothèque de logique métier et utilitaires partagés pour l'écosystème SAMATRANSPORT", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./auth": "./src/auth/index.ts", "./api": "./src/api/index.ts", "./i18n": "./src/i18n/index.ts", "./utils": "./src/utils/index.ts", "./types": "./src/types/index.ts"}, "scripts": {"build": "tsc --noEmit", "lint": "eslint src/", "type-check": "tsc --noEmit", "test": "vitest", "test:watch": "vitest --watch", "clean": "rm -rf .turbo && rm -rf node_modules"}, "dependencies": {"@supabase/supabase-js": "catalog:", "@supabase/auth-helpers-nextjs": "catalog:", "next-auth": "catalog:", "next-intl": "catalog:", "zod": "catalog:", "date-fns": "catalog:", "zustand": "catalog:", "@tanstack/react-query": "catalog:"}, "devDependencies": {"@types/node": "catalog:", "typescript": "catalog:", "vitest": "catalog:", "@testing-library/jest-dom": "catalog:"}, "peerDependencies": {"react": "^18.0.0", "next": "^14.0.0"}, "files": ["src"]}