{"name": "@commitlint/cli", "version": "18.6.1", "description": "Lint your commit messages", "files": ["index.js", "cli.js", "lib"], "bin": {"commitlint": "./cli.js"}, "scripts": {"deps": "dep-check", "pkg": "pkg-check"}, "engines": {"node": ">=v18"}, "repository": {"type": "git", "url": "https://github.com/conventional-changelog/commitlint.git", "directory": "@commitlint/cli"}, "bugs": {"url": "https://github.com/conventional-changelog/commitlint/issues"}, "homepage": "https://commitlint.js.org/", "keywords": ["conventional-changelog", "commitlint", "cli"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"@commitlint/test": "^18.0.0", "@commitlint/utils": "^18.6.1", "@types/lodash.isfunction": "^3.0.8", "@types/lodash.merge": "^4.6.8", "@types/node": "^18.11.9", "@types/yargs": "^17.0.29", "fs-extra": "^11.0.0", "lodash.merge": "^4.6.2"}, "dependencies": {"@commitlint/format": "^18.6.1", "@commitlint/lint": "^18.6.1", "@commitlint/load": "^18.6.1", "@commitlint/read": "^18.6.1", "@commitlint/types": "^18.6.1", "execa": "^5.0.0", "lodash.isfunction": "^3.0.9", "resolve-from": "5.0.0", "resolve-global": "1.0.0", "yargs": "^17.0.0"}, "gitHead": "89f5bf91d1c11b1f457a6f0d99b8ea34583a9311"}