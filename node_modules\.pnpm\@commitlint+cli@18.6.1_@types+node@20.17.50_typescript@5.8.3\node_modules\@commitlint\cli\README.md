> Lint commit messages

<p align="center">
  <img width="750" src="https://conventional-changelog.github.io/commitlint/assets/commitlint.svg">
</p>

# @commitlint/cli

## Getting started

```
npm install --save-dev @commitlint/cli @commitlint/config-angular
echo "module.exports = {extends: ['@commitlint/config-angular']};" > commitlint.config.js
```

Consult [docs/cli](https://conventional-changelog.github.io/commitlint/#/reference-cli) for comprehensive documentation.
