{"compilerOptions": {"target": "ES2022", "lib": ["dom", "dom.iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@samatransport/ui": ["./packages/ui/src"], "@samatransport/ui/*": ["./packages/ui/src/*"], "@samatransport/lib-core": ["./packages/lib-core/src"], "@samatransport/lib-core/*": ["./packages/lib-core/src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next", "dist", "build"]}