{"name": "@samatransport/ui", "version": "0.1.0", "description": "SAMATRANSPORT Design System - Composants UI réutilisables", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./styles": "./src/styles/globals.css"}, "scripts": {"dev": "storybook dev -p 6006", "build": "tsc --noEmit", "build-storybook": "storybook build", "lint": "eslint src/", "type-check": "tsc --noEmit", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf storybook-static"}, "dependencies": {"react": "catalog:", "react-dom": "catalog:", "@headlessui/react": "catalog:", "@heroicons/react": "catalog:", "lucide-react": "catalog:", "clsx": "catalog:", "class-variance-authority": "catalog:", "react-hook-form": "catalog:", "zod": "catalog:", "@hookform/resolvers": "catalog:"}, "devDependencies": {"@types/react": "catalog:", "@types/react-dom": "catalog:", "typescript": "catalog:", "tailwindcss": "catalog:", "@tailwindcss/forms": "catalog:", "@tailwindcss/typography": "catalog:", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@storybook/react": "catalog:", "@storybook/nextjs": "catalog:", "@storybook/addon-essentials": "^7.6.4", "@storybook/addon-interactions": "^7.6.4", "@storybook/addon-links": "^7.6.4", "@storybook/blocks": "^7.6.4", "@storybook/testing-library": "^0.2.2", "storybook": "^7.6.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "files": ["src"]}