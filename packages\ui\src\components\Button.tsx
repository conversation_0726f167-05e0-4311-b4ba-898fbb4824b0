import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../utils/cn';

/**
 * Variantes du composant Button
 */
const buttonVariants = cva(
  // Classes de base
  'inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none',
  {
    variants: {
      variant: {
        primary: 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-sm',
        secondary: 'bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500 shadow-sm',
        outline: 'border border-neutral-300 bg-white text-neutral-700 hover:bg-neutral-50 focus:ring-primary-500',
        ghost: 'text-neutral-700 hover:bg-neutral-100 focus:ring-primary-500',
        danger: 'bg-error-600 text-white hover:bg-error-700 focus:ring-error-500 shadow-sm',
        success: 'bg-success-600 text-white hover:bg-success-700 focus:ring-success-500 shadow-sm',
        warning: 'bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500 shadow-sm',
      },
      size: {
        sm: 'h-8 px-3 text-xs',
        md: 'h-10 px-4 text-sm',
        lg: 'h-12 px-6 text-base',
        xl: 'h-14 px-8 text-lg',
      },
      fullWidth: {
        true: 'w-full',
        false: 'w-auto',
      },
    },
    defaultVariants: {
      variant: 'primary',
      size: 'md',
      fullWidth: false,
    },
  }
);

/**
 * Interface des props du composant Button
 */
export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  /** Contenu du bouton */
  children: React.ReactNode;
  /** Icône à afficher avant le texte */
  startIcon?: React.ReactNode;
  /** Icône à afficher après le texte */
  endIcon?: React.ReactNode;
  /** État de chargement */
  loading?: boolean;
  /** Texte à afficher pendant le chargement */
  loadingText?: string;
  /** Référence vers l'élément DOM */
  ref?: React.Ref<HTMLButtonElement>;
}

/**
 * Composant Button du Design System SAMATRANSPORT
 * 
 * @example
 * <Button variant="primary" size="md">
 *   Réserver un billet
 * </Button>
 * 
 * @example
 * <Button variant="outline" startIcon={<PlusIcon />} loading>
 *   Ajouter
 * </Button>
 */
export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      fullWidth,
      children,
      startIcon,
      endIcon,
      loading = false,
      loadingText,
      disabled,
      ...props
    },
    ref
  ) => {
    const isDisabled = disabled || loading;

    return (
      <button
        className={cn(buttonVariants({ variant, size, fullWidth, className }))}
        disabled={isDisabled}
        ref={ref}
        {...props}
      >
        {/* Icône de début ou spinner de chargement */}
        {loading ? (
          <svg
            className="mr-2 h-4 w-4 animate-spin"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        ) : startIcon ? (
          <span className="mr-2 flex-shrink-0">{startIcon}</span>
        ) : null}

        {/* Contenu du bouton */}
        <span className="truncate">
          {loading && loadingText ? loadingText : children}
        </span>

        {/* Icône de fin */}
        {!loading && endIcon && (
          <span className="ml-2 flex-shrink-0">{endIcon}</span>
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';

export default Button;
